from django.shortcuts import render,redirect
from django.http import HttpResponse
# Create your views here.

def login(request):
    if request.method == 'POST':
        username = request.POST.get('username', None)
        password = request.POST.get('password', None)
        message = '所有字段都必须填写！'
        if username and password:
            if username == 'admin' and password == '123':
                return redirect('/index')
            else:
                message = '用户名或密码不正确！'
                return render(request, 'login.html', {"error": message})
    return render(request, 'login.html')


def index(request):
    return render(request, 'index.html')